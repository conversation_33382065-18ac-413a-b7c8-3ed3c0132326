import { LOCALES, routing } from "@/i18n/routing";
import { getTranslations, setRequestLocale } from "next-intl/server";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

const HomePage = async ({
  params,
}: {
  params: Promise<{ locale: (typeof LOCALES)[number] }>;
}) => {
  const t = await getTranslations("Metadata");
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return <h1>{t("title")}</h1>;
};

export default HomePage;
